<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <h1>专家出诊屏</h1>
  </div>
</template>

<script>
export default {
  name: "specialistScreen",
  data() {
    return {};
  },
  created() {
    this.getDepartmentList();
  },
  methods: {
    // 获取科室列表
    async getDepartmentList() {
      const res = await this.$api.department.queryDepartment();
      console.log("二级科室列表", res);
      if (res.success) {
        console.log("请求成功");
      } else {
        this.$message.error(res.message);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
}
</style>
